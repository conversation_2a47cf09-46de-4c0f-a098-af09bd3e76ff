import pandas as pd
import requests
import time
import json
import math
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple, Optional
import threading

def dms_to_decimal(degrees, minutes, seconds):
    """将度分秒转换为十进制度数"""
    return degrees + minutes/60 + seconds/3600

class GasStationCoordinateQuery:
    """加油站坐标查询和对比工具"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        self.results = []

        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
    def geocode_address(self, address: str, region: str = "tw") -> Optional[Dict]:
        """使用Google Geocoding API查询地址坐标"""
        if not address or not address.strip():
            return {
                'lat': None,
                'lng': None,
                'formatted_address': None,
                'status': "ERROR: Empty address"
            }
        
        params = {
            'address': address,
            'key': self.api_key,
            'language': 'zh-TW',
            'region': region
        }

        try:
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                return {
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'formatted_address': data['results'][0]['formatted_address'],
                    'status': 'SUCCESS'
                }
            else:
                error_msg = data.get('error_message', data['status'])
                return {
                    'lat': None,
                    'lng': None,
                    'formatted_address': None,
                    'status': f"ERROR: {error_msg}"
                }
        except requests.exceptions.Timeout:
            return {
                'lat': None,
                'lng': None,
                'formatted_address': None,
                'status': "ERROR: Request timeout"
            }
        except requests.exceptions.RequestException as e:
            return {
                'lat': None,
                'lng': None,
                'formatted_address': None,
                'status': f"ERROR: Request failed - {str(e)}"
            }
        except Exception as e:
            return {
                'lat': None,
                'lng': None,
                'formatted_address': None,
                'status': f"EXCEPTION: {str(e)}"
            }

    def geocode_single_query(self, query_data: Dict) -> Dict:
        """单个查询任务，用于并发执行"""
        index = query_data['index']
        query = query_data['query']
        query_type = query_data['type']  # 'query1' or 'query2'

        result = self.geocode_address(query)

        return {
            'index': index,
            'type': query_type,
            'query': query,
            'result': result
        }
    
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """计算两点间的距离（米）"""
        if None in [lat1, lng1, lat2, lng2]:
            return float('inf')
            
        # 使用Haversine公式计算距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng/2) * math.sin(delta_lng/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def process_batch_queries(self, query_tasks: List[Dict], max_workers: int = 500) -> Dict:
        """批次处理查询任务（优化版：确保所有任务完成才返回）"""
        query_results = {}
        completed_count = 0
        total_tasks = len(query_tasks)
        failed_tasks = []
        max_retries = 3

        print(f"开始批次查询，共 {total_tasks} 个任务，使用 {max_workers} 个并发线程...")

        while True:
            retry_tasks = []
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交当前轮次的任务
                current_tasks = failed_tasks if failed_tasks else query_tasks
                future_to_task = {executor.submit(self.geocode_single_query, task): task for task in current_tasks}

                # 收集结果
                for future in as_completed(future_to_task):
                    try:
                        result = future.result()
                        index = result['index']
                        query_type = result['type']

                        if index not in query_results:
                            query_results[index] = {}

                        query_results[index][query_type] = result['result']

                        completed_count += 1
                        if completed_count % 200 == 0:
                            print(f"已完成 {completed_count}/{total_tasks} 个查询...")

                    except Exception as e:
                        print(f"查询任务失败: {e}")
                        # 将失败的任务加入重试列表
                        task = future_to_task[future]
                        task_data = task.copy()
                        task_data['retry_count'] = task.get('retry_count', 0) + 1
                        if task_data['retry_count'] <= max_retries:
                            retry_tasks.append(task_data)

            # 检查是否所有任务都完成
            if not retry_tasks:
                break
            
            print(f"有 {len(retry_tasks)} 个任务需要重试，等待2秒后重试...")
            failed_tasks = retry_tasks
            time.sleep(2)

        print(f"批次查询完成！成功 {completed_count}/{total_tasks} 个查询")
        return query_results

    def generate_alternative_queries(self, row_info: Dict) -> List[Dict]:
        """为失败或误差大的查询生成替代查询策略（增强版）"""
        county = row_info['county']
        district = row_info['district']
        brand = row_info['brand']
        station_name = row_info['station_name']
        address = row_info['address']

        alternative_queries = []

        # 策略1: 只用地址
        if address:
            alternative_queries.append({
                'query': address,
                'strategy': 'address_only'
            })

        # 策略2: 县市+地址
        if county and address:
            alternative_queries.append({
                'query': f"{county} {address}",
                'strategy': 'county_address'
            })

        # 策略3: 县市+区+加油站名称
        if county and district and station_name:
            alternative_queries.append({
                'query': f"{county} {district} {station_name}",
                'strategy': 'location_station'
            })

        # 策略4: 品牌+加油站名称+县市
        if brand and station_name and county:
            alternative_queries.append({
                'query': f"{brand} {station_name} {county}",
                'strategy': 'brand_station_county'
            })

        # 策略5: 简化地址（去掉号码）
        if address:
            import re
            simplified_address = re.sub(r'\d+号?$', '', address)
            if simplified_address != address:
                alternative_queries.append({
                    'query': simplified_address,
                    'strategy': 'simplified_address'
                })

        # 策略6: 只用加油站名称+区
        if station_name and district:
            alternative_queries.append({
                'query': f"{district} {station_name}",
                'strategy': 'district_station'
            })

        # 策略7: 地址中的关键字（去掉路/街/巷等详细）
        if address:
            # 尝试提取地址的主要部分
            road_parts = re.split(r'[路街道巷弄]', address)
            if len(road_parts) > 1:
                main_address = road_parts[0] + '路'
                alternative_queries.append({
                    'query': main_address,
                    'strategy': 'main_road'
                })

        # 策略8: 省略品牌名称
        if county and district and station_name:
            # 如果加油站名称包含品牌，尝试省略品牌
            clean_station_name = station_name
            for brand_name in ['中油', '台塑', '台糖', '全國', '統一']:
                if brand_name in station_name:
                    clean_station_name = station_name.replace(brand_name, '').strip()
                    break
            
            if clean_station_name != station_name:
                alternative_queries.append({
                    'query': f"{county} {district} {clean_station_name}",
                    'strategy': 'clean_station_name'
                })

        return alternative_queries

    def _verify_batch_completion(self, batch_data: List[Dict], results: Dict) -> List[int]:
        """验证批次中所有查询是否都完成，返回未完成的索引列表"""
        incomplete_indices = []
        
        for i, row_info in enumerate(batch_data):
            index = row_info['index']
            index_results = results.get(index, {})
            
            # 检查是否两个查询都有坐标
            query1_has_coords = False
            query2_has_coords = False
            
            # 检查query1和其重试
            for query_type in ['query1', 'query1_retry']:
                if query_type in index_results and index_results[query_type].get('lat'):
                    query1_has_coords = True
                    break
            
            # 检查query2和其重试
            for query_type in ['query2', 'query2_retry']:
                if query_type in index_results and index_results[query_type].get('lat'):
                    query2_has_coords = True
                    break
            
            # 只有当两个查询都有坐标时才算完成
            if not (query1_has_coords and query2_has_coords):
                incomplete_indices.append(index)
        
        return incomplete_indices

    def debug_failed_queries(self, batch_data: List[Dict], results: Dict):
        """调试失败的查询，输出失败原因"""
        failed_samples = []
        
        for i, row_info in enumerate(batch_data[:10]):  # 只检查前10个
            index = row_info['index']
            index_results = results.get(index, {})
            
            query1_result = index_results.get('query1', {})
            query2_result = index_results.get('query2', {})
            
            if not query1_result.get('lat') or not query2_result.get('lat'):
                failed_samples.append({
                    'index': index,
                    'query1': row_info['query1'],
                    'query2': row_info['query2'],
                    'query1_status': query1_result.get('status', 'NO_RESULT'),
                    'query2_status': query2_result.get('status', 'NO_RESULT')
                })
        
        if failed_samples:
            print(f"\n=== 失败查询样本（前10个）===")
            for sample in failed_samples:
                print(f"索引 {sample['index']}:")
                print(f"  查询1: '{sample['query1']}' -> {sample['query1_status']}")
                print(f"  查询2: '{sample['query2']}' -> {sample['query2_status']}")
                print()

    def optimize_high_error_coordinates(self, error_item: Dict) -> Dict:
        """专门优化误差大的坐标查询"""
        row_info = error_item['row_info']
        original_result1 = error_item['result1']
        original_result2 = error_item['result2']
        original_distance = error_item['distance']
        
        best_result1 = original_result1
        best_result2 = original_result2
        best_distance = original_distance
        best_strategy = 'original'
        
        # 生成更多的优化查询策略
        optimization_queries = []
        
        # 策略1: 尝试不同的地址格式
        if row_info['address']:
            # 去掉段號
            import re
            clean_address = re.sub(r'\d+段\d+號?', '', row_info['address'])
            if clean_address != row_info['address']:
                optimization_queries.append({
                    'query': clean_address,
                    'strategy': 'clean_address'
                })
        
        # 策略2: 只用加油站名称
        if row_info['station_name']:
            optimization_queries.append({
                'query': row_info['station_name'],
                'strategy': 'station_only'
            })
        
        # 策略3: 用县市+加油站名称
        if row_info['county'] and row_info['station_name']:
            optimization_queries.append({
                'query': f"{row_info['county']} {row_info['station_name']}",
                'strategy': 'county_station'
            })
        
        # 策略4: 用附近的交叉路口
        if row_info['address']:
            # 尝试找到地址中的主要道路
            import re
            road_match = re.search(r'([^路街道巷弄]+)[路街道巷弄]', row_info['address'])
            if road_match:
                main_road = road_match.group(1)
                optimization_queries.append({
                    'query': f"{main_road}附近",
                    'strategy': 'nearby_road'
                })
        
        # 执行优化查询
        if optimization_queries:
            optimization_tasks = []
            for i, opt_query in enumerate(optimization_queries[:3]):  # 最多尝试3种
                optimization_tasks.append({
                    'index': row_info['index'],
                    'query': opt_query['query'],
                    'type': f'opt_{i+1}',
                    'strategy': opt_query['strategy']
                })
            
            optimization_results = self.process_batch_queries(optimization_tasks, max_workers=50)
            
            # 测试每个优化结果
            for opt_type in ['opt_1', 'opt_2', 'opt_3']:
                opt_result = optimization_results.get(row_info['index'], {}).get(opt_type)
                if opt_result and opt_result.get('lat'):
                    # 尝试替换result1
                    distance1 = self.calculate_distance(
                        opt_result.get('lat'), opt_result.get('lng'),
                        original_result2.get('lat'), original_result2.get('lng')
                    )
                    
                    # 尝试替换result2
                    distance2 = self.calculate_distance(
                        original_result1.get('lat'), original_result1.get('lng'),
                        opt_result.get('lat'), opt_result.get('lng')
                    )
                    
                    # 选择误差更小的方案
                    if distance1 != float('inf') and distance1 < best_distance:
                        best_distance = distance1
                        best_result1 = opt_result
                        best_result2 = original_result2
                        best_strategy = f'{opt_type}_as_result1'
                    
                    if distance2 != float('inf') and distance2 < best_distance:
                        best_distance = distance2
                        best_result1 = original_result1
                        best_result2 = opt_result
                        best_strategy = f'{opt_type}_as_result2'
        
        return {
            'best_result1': best_result1,
            'best_result2': best_result2,
            'best_distance': best_distance,
            'best_strategy': best_strategy,
            'original_distance': original_distance
        }

    def process_excel_file_optimized(self, file_path: str, max_workers: int = 500, batch_size: int = 1000, error_threshold: float = 1000.0) -> List[Dict]:
        """优化的Excel文件处理：批次处理+二次查询+误差优化（确保所有坐标获取到才进行下一批）"""
        print(f"正在读取Excel文件: {file_path}")

        try:
            df = pd.read_excel(file_path)
            print(f"文件读取成功，共 {len(df)} 行数据")
            print(f"列名: {df.columns.tolist()}")
            print(f"前5行数据:")
            print(df.head())

        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return []

        # 准备行数据
        row_data = []
        for index, row in df.iterrows():
            col2 = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""  # 縣市
            col3 = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""  # 鄉鎮市區
            col4 = str(row.iloc[3]) if pd.notna(row.iloc[3]) else ""  # 品牌
            col5 = str(row.iloc[4]) if pd.notna(row.iloc[4]) else ""  # 加油站名稱
            col6 = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ""  # 地址

            query1 = f"{col2} {col3} {col4} {col5}".strip()
            query2 = col6.strip()

            row_data.append({
                'index': index,
                'county': col2,
                'district': col3,
                'brand': col4,
                'station_name': col5,
                'address': col6,
                'query1': query1,
                'query2': query2
            })

        total_rows = len(row_data)
        print(f"\n=== 第一轮查询 ===")
        print(f"总数据量: {total_rows} 行")
        print(f"批次大小: {batch_size}")
        print(f"并发数: {max_workers}")

        # 分批处理
        all_results = {}
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_data = row_data[batch_start:batch_end]

            print(f"\n处理批次 {batch_start//batch_size + 1}: 行 {batch_start+1}-{batch_end}")

            # 准备批次查询任务
            batch_tasks = []
            for row_info in batch_data:
                batch_tasks.extend([
                    {'index': row_info['index'], 'query': row_info['query1'], 'type': 'query1'},
                    {'index': row_info['index'], 'query': row_info['query2'], 'type': 'query2'}
                ])

            # 执行批次查询（确保所有任务完成）
            batch_results = self.process_batch_queries(batch_tasks, max_workers)
            all_results.update(batch_results)

            # 验证当前批次是否所有坐标都获取到
            incomplete_indices = self._verify_batch_completion(batch_data, batch_results)
            
            # 如果有失败的查询，输出调试信息
            if incomplete_indices:
                self.debug_failed_queries(batch_data, batch_results)
            
            # 如果有不完整的查询，进行重试
            retry_count = 0
            while incomplete_indices and retry_count < 3:
                print(f"发现 {len(incomplete_indices)} 个查询不完整，进行第 {retry_count + 1} 次重试...")
                
                retry_tasks = []
                for index in incomplete_indices:
                    row_info = batch_data[index - batch_start]
                    retry_tasks.extend([
                        {'index': row_info['index'], 'query': row_info['query1'], 'type': 'query1_retry'},
                        {'index': row_info['index'], 'query': row_info['query2'], 'type': 'query2_retry'}
                    ])
                
                retry_results = self.process_batch_queries(retry_tasks, max_workers)
                
                # 更新结果
                for index, results in retry_results.items():
                    if index not in all_results:
                        all_results[index] = {}
                    all_results[index].update(results)
                
                # 重新验证
                incomplete_indices = self._verify_batch_completion(batch_data, all_results)
                retry_count += 1
                
                if incomplete_indices:
                    print(f"重试后仍有 {len(incomplete_indices)} 个查询不完整，等待3秒...")
                    time.sleep(3)

            print(f"批次 {batch_start//batch_size + 1} 完成，成功率: {len(batch_data) - len(incomplete_indices)}/{len(batch_data)}")

            # 批次间等待，避免API限制
            if batch_end < total_rows:
                print("等待2秒后处理下一批...")
                time.sleep(2)

        print(f"\n=== 第一轮查询完成，开始分析结果 ===")

        # 分析第一轮结果
        failed_queries = []
        high_error_queries = []
        successful_results = []

        for row_info in row_data:
            index = row_info['index']
            result1 = all_results.get(index, {}).get('query1', {})
            result2 = all_results.get(index, {}).get('query2', {})

            # 检查是否有失败的查询
            if (not result1.get('lat') or not result2.get('lat')):
                failed_queries.append(row_info)
                continue

            # 计算距离误差
            distance = self.calculate_distance(
                result1.get('lat'), result1.get('lng'),
                result2.get('lat'), result2.get('lng')
            )

            # 检查误差是否过大
            if distance != float('inf') and distance > error_threshold:
                high_error_queries.append({
                    'row_info': row_info,
                    'result1': result1,
                    'result2': result2,
                    'distance': distance
                })
            else:
                # 成功的结果
                result_data = {
                    'row_index': index + 1,
                    'county': row_info['county'],
                    'district': row_info['district'],
                    'brand': row_info['brand'],
                    'station_name': row_info['station_name'],
                    'address': row_info['address'],
                    'query1': row_info['query1'],
                    'query2': row_info['query2'],
                    'result1_lat': result1.get('lat'),
                    'result1_lng': result1.get('lng'),
                    'result1_address': result1.get('formatted_address'),
                    'result1_status': result1.get('status'),
                    'result2_lat': result2.get('lat'),
                    'result2_lng': result2.get('lng'),
                    'result2_address': result2.get('formatted_address'),
                    'result2_status': result2.get('status'),
                    'distance_error_meters': distance if distance != float('inf') else None,
                    'query_round': 1
                }
                successful_results.append(result_data)

        print(f"第一轮结果统计:")
        print(f"  成功: {len(successful_results)} 条")
        print(f"  失败需重试: {len(failed_queries)} 条")
        print(f"  误差过大需优化: {len(high_error_queries)} 条")

        # 第二轮查询：处理失败的查询
        if failed_queries:
            print(f"\n=== 第二轮查询：处理失败的 {len(failed_queries)} 条记录 ===")

            second_round_tasks = []
            for row_info in failed_queries:
                alternatives = self.generate_alternative_queries(row_info)
                for i, alt in enumerate(alternatives[:3]):  # 最多尝试3种策略
                    second_round_tasks.append({
                        'index': row_info['index'],
                        'query': alt['query'],
                        'type': f'alt_{i+1}',
                        'strategy': alt['strategy']
                    })

            if second_round_tasks:
                second_results = self.process_batch_queries(second_round_tasks, max_workers)

                # 处理第二轮结果
                for row_info in failed_queries:
                    index = row_info['index']
                    best_result = None
                    best_strategy = None

                    # 找到最好的替代查询结果
                    for alt_type in ['alt_1', 'alt_2', 'alt_3']:
                        alt_result = second_results.get(index, {}).get(alt_type)
                        if alt_result and alt_result.get('lat'):
                            best_result = alt_result
                            best_strategy = alt_type
                            break

                    if best_result:
                        # 使用最好的替代结果
                        original_result1 = all_results.get(index, {}).get('query1', {})
                        original_result2 = all_results.get(index, {}).get('query2', {})

                        # 如果原始查询1失败，用替代结果
                        if not original_result1.get('lat'):
                            result1 = best_result
                        else:
                            result1 = original_result1

                        # 如果原始查询2失败，用替代结果
                        if not original_result2.get('lat'):
                            result2 = best_result
                        else:
                            result2 = original_result2

                        distance = self.calculate_distance(
                            result1.get('lat'), result1.get('lng'),
                            result2.get('lat'), result2.get('lng')
                        )

                        result_data = {
                            'row_index': index + 1,
                            'county': row_info['county'],
                            'district': row_info['district'],
                            'brand': row_info['brand'],
                            'station_name': row_info['station_name'],
                            'address': row_info['address'],
                            'query1': row_info['query1'],
                            'query2': row_info['query2'],
                            'result1_lat': result1.get('lat'),
                            'result1_lng': result1.get('lng'),
                            'result1_address': result1.get('formatted_address'),
                            'result1_status': result1.get('status'),
                            'result2_lat': result2.get('lat'),
                            'result2_lng': result2.get('lng'),
                            'result2_address': result2.get('formatted_address'),
                            'result2_status': result2.get('status'),
                            'distance_error_meters': distance if distance != float('inf') else None,
                            'query_round': 2,
                            'best_strategy': best_strategy
                        }
                        successful_results.append(result_data)

        # 第三轮查询：优化误差过大的查询（使用专门的优化方法）
        if high_error_queries:
            print(f"\n=== 第三轮查询：优化误差过大的 {len(high_error_queries)} 条记录 ===")

            # 分批处理误差大的查询，避免内存问题
            batch_size = 100
            for batch_start in range(0, len(high_error_queries), batch_size):
                batch_end = min(batch_start + batch_size, len(high_error_queries))
                batch_error_items = high_error_queries[batch_start:batch_end]
                
                print(f"处理误差优化批次 {batch_start//batch_size + 1}: 记录 {batch_start+1}-{batch_end}")
                
                # 批量优化
                optimized_results = []
                for error_item in batch_error_items:
                    optimized_result = self.optimize_high_error_coordinates(error_item)
                    optimized_results.append(optimized_result)
                
                # 添加优化后的结果
                for i, error_item in enumerate(batch_error_items):
                    row_info = error_item['row_info']
                    optimized_result = optimized_results[i]
                    
                    result_data = {
                        'row_index': row_info['index'] + 1,
                        'county': row_info['county'],
                        'district': row_info['district'],
                        'brand': row_info['brand'],
                        'station_name': row_info['station_name'],
                        'address': row_info['address'],
                        'query1': row_info['query1'],
                        'query2': row_info['query2'],
                        'result1_lat': optimized_result['best_result1'].get('lat'),
                        'result1_lng': optimized_result['best_result1'].get('lng'),
                        'result1_address': optimized_result['best_result1'].get('formatted_address'),
                        'result1_status': optimized_result['best_result1'].get('status'),
                        'result2_lat': optimized_result['best_result2'].get('lat'),
                        'result2_lng': optimized_result['best_result2'].get('lng'),
                        'result2_address': optimized_result['best_result2'].get('formatted_address'),
                        'result2_status': optimized_result['best_result2'].get('status'),
                        'distance_error_meters': optimized_result['best_distance'] if optimized_result['best_distance'] != float('inf') else None,
                        'query_round': 3,
                        'optimization_strategy': optimized_result['best_strategy'],
                        'original_error': optimized_result['original_distance']
                    }
                    successful_results.append(result_data)
                
                # 批次间等待
                if batch_end < len(high_error_queries):
                    print("等待2秒后处理下一批...")
                    time.sleep(2)
            
            print(f"第三轮优化完成，处理了 {len(high_error_queries)} 条误差大的记录")

        print(f"\n=== 最终结果统计 ===")
        print(f"总处理记录数: {len(successful_results)}")

        # 按查询轮次统计
        round_stats = {}
        for result in successful_results:
            round_num = result.get('query_round', 1)
            if round_num not in round_stats:
                round_stats[round_num] = 0
            round_stats[round_num] += 1

        for round_num, count in sorted(round_stats.items()):
            print(f"第{round_num}轮成功: {count} 条")

        return successful_results
    
    def save_results(self, results: List[Dict], output_file: str = "coordinate_comparison_results.xlsx"):
        """保存结果到Excel文件"""
        if not results:
            print("没有结果可保存")
            return
        
        df_results = pd.DataFrame(results)
        
        # 添加统计信息
        valid_distances = [r for r in results if r['distance_error_meters'] is not None]
        
        if valid_distances:
            distances = [r['distance_error_meters'] for r in valid_distances]
            stats = {
                'total_queries': len(results),
                'successful_comparisons': len(valid_distances),
                'avg_distance_error': sum(distances) / len(distances),
                'max_distance_error': max(distances),
                'min_distance_error': min(distances),
                'median_distance_error': sorted(distances)[len(distances)//2]
            }
            
            print(f"\n=== 统计结果 ===")
            print(f"总查询数: {stats['total_queries']}")
            print(f"成功对比数: {stats['successful_comparisons']}")
            print(f"平均误差: {stats['avg_distance_error']:.2f} 米")
            print(f"最大误差: {stats['max_distance_error']:.2f} 米")
            print(f"最小误差: {stats['min_distance_error']:.2f} 米")
            print(f"中位数误差: {stats['median_distance_error']:.2f} 米")
        
        try:
            df_results.to_excel(output_file, index=False)
            print(f"\n结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")

def main():
    # Google API密钥
    API_KEY = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"

    # Excel文件路径
    EXCEL_FILE = "全部縣市加油站位置.xlsx"

    # 创建查询器
    query_tool = GasStationCoordinateQuery(API_KEY)

    print("开始处理加油站坐标查询（优化模式，降低并发避免API限制）...")
    print("=" * 50)

    start_time = time.time()

    # 优化并发处理Excel文件（降低并发避免API限制）
    results = query_tool.process_excel_file_optimized(
        EXCEL_FILE, 
        max_workers=50,  # 降低并发数避免API限制
        batch_size=500,  # 减小批次大小
        error_threshold=1000.0
    )

    end_time = time.time()
    print(f"\n查询耗时: {end_time - start_time:.2f} 秒")

    # 保存结果
    if results:
        query_tool.save_results(results)
        print("\n处理完成！")
    else:
        print("\n处理失败，没有获得有效结果。")

if __name__ == "__main__":
    main()
